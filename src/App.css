body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: #f9fafb;
  color: #111;
  height: 100vh;
  overflow: hidden;
}

.layout {
  display: flex;
  height: 100vh;
  width: 100vw;
}

/* SIDEBARS */
.sidebar {
  width: 260px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
  box-sizing: border-box;
}

.sidebar.right {
  border-right: none;
  border-left: 1px solid #e5e7eb;
}

.sidebar h3 {
  margin-top: 1rem;
  color: #646cff;
  font-size: 1.2rem;
}

/* CHATS HEADER */
.chats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
}

.chats-header h3 {
  margin: 0;
}

.archive-toggle {
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  opacity: 0.6;
}

.archive-toggle:hover {
  background-color: #f3f4f6;
  opacity: 1;
}

.archive-toggle.active {
  opacity: 1;
  background-color: #e0e7ff;
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  margin-bottom: 0.5rem;
}

/* CHAT ITEM STYLES */
.chat-item {
  position: relative;
  margin-bottom: 0.5rem;
}

.chat-item-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chat-options {
  position: relative;
  display: flex;
  align-items: center;
}

.chat-button {
  background: none;
  border: none;
  font-size: 1rem;
  color: #333;
  text-align: left;
  flex: 1;
  cursor: pointer;
  padding: 0.4rem 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.chat-button:hover {
  background-color: #f3f4f6;
}

.chat-button.active {
  background-color: #e0e7ff;
  font-weight: bold;
  color: #111;
}

.options-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #888;
  cursor: pointer;
  padding: 0.3rem 0.5rem;
  border-radius: 6px;
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.chat-item:hover .options-button {
  opacity: 1;
}

.options-button:hover {
  background-color: #3a3a3a;
  color: #fff;
  transform: scale(1.05);
}

/* DROPDOWN MENU */
.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 160px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: dropdownFadeIn 0.15s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 400;
}

.dropdown-item:hover {
  background-color: #3a3a3a;
  color: #ffffff;
}

.dropdown-item.delete {
  color: #ff6b6b;
}

.dropdown-item.delete:hover {
  background-color: #4a2626;
  color: #ff8a8a;
}

.dropdown-icon {
  font-size: 1.1rem;
  width: 1.2rem;
  height: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
}

/* CHAT EDIT INPUT */
.chat-edit {
  padding: 0.2rem 0;
}

.chat-edit-input {
  width: 100%;
  padding: 0.4rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 1rem;
  background: white;
  color: #111 !important; /* Force dark text color */
  font-family: inherit;
}

.chat-edit-input:focus {
  outline: none;
  border-color: #646cff;
  box-shadow: 0 0 0 1px #646cff;
  color: #111 !important; /* Ensure text stays dark on focus */
}

/* DELETE CONFIRMATION MODAL */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.delete-confirm-modal {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.delete-confirm-modal h3 {
  margin: 0 0 0.5rem 0;
  color: #dc2626;
  font-size: 1.25rem;
}

.delete-confirm-modal p {
  margin: 0 0 1.5rem 0;
  color: #6b7280;
  line-height: 1.5;
}

.delete-confirm-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.cancel-button {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.cancel-button:hover {
  background-color: #f9fafb;
}

.delete-button {
  padding: 0.5rem 1rem;
  border: none;
  background: #dc2626;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.delete-button:hover {
  background-color: #b91c1c;
}

/* Legacy sidebar button styles for compatibility */
.sidebar button {
  background: none;
  border: none;
  font-size: 1rem;
  color: #111;
  text-align: left;
  width: 100%;
  cursor: pointer;
  color: #333;
  padding: 0.4rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.sidebar button:hover {
  background-color: #f3f4f6;
}

.sidebar button.active {
  font-weight: bold;
  color: #111;
}

.sidebar button.active {
  font-weight: bold;
  color: #111;
}

.columns-list {
  padding-left: 20px;
}
.columns-list li {
  margin-bottom: 5px;
}

/* MAIN AREA */
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* CHAT WINDOW */
.chat-window {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  box-sizing: border-box;
}

.message {
  max-width: 75%;
  padding: 1rem;
  border-radius: 8px;
  white-space: pre-wrap;
}

.message.user {
  background: #e0e7ff;
  align-self: flex-end;
}

.message.assistant {
  background: #f1f5f9;
  align-self: flex-start;
}

/* PROMPT BAR */
.prompt-bar {
  display: flex;
  padding: 1rem;
  background: #f9fafb;
  position: relative;
  align-items: flex-end;
}

.prompt-bar .textarea-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: flex-end;
}

.prompt-bar textarea {
  flex: 1;
  resize: none;
  padding: 0.75rem 3rem 0.75rem 0.75rem;
  font-size: 1rem;
  border: 1px solid #f4aaee;
  color: #620dca;
  border-radius: 8px;
  max-height: 120px;
  background: #f9fafb;
  min-height: 50px;
}

.prompt-bar button {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: #10a37f;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* SEARCH INPUT */
.sidebar.right input[type="text"] {
  width: 90%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #f4aaee;
  border-radius: 4px;
  background-color: #f6d0d0;
  color: #620dca;
  font-size: 1rem;
}

/* WELCOME SCREEN */
.welcome-screen {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  color: #620dca;
}

.prompt-bar.centered {
  width: 60%;
  margin-top: 1rem;
}

/* ERROR BANNER */
.error-banner {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  margin: 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.error-banner p {
  margin: 0.25rem 0;
}

/* LOADING STATES */
.prompt-bar button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #10a37f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .prompt-bar.centered {
    width: 90%;
  }

  .sidebar.left,
  .sidebar.right {
    display: none;
  }

  .main {
    width: 100vw;
  }

  .prompt-bar {
    padding: 0.75rem;
  }

  .chat-window {
    padding: 1rem;
  }
}
