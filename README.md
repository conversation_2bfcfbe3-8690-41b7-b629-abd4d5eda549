# My SaaS Frontend

A React-based chat interface for AI-powered database query generation using natural language. This frontend connects to a FastAPI + <PERSON><PERSON><PERSON><PERSON> backend that converts natural language queries into SQL.

## Features

- **Chat Interface**: Create and manage multiple chat sessions
- **Database Integration**: Browse database tables and schemas
- **Natural Language Queries**: Send natural language queries to generate SQL
- **Real-time Responses**: Get AI-generated responses and query results
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: React 19 + Vite
- **HTTP Client**: Axios for API communication
- **Styling**: Custom CSS with responsive design
- **State Management**: React hooks with custom API hooks

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (version 18.0 or higher): [Download Node.js](https://nodejs.org/)
- **npm** (comes with Node.js) or **yarn** package manager
- **Git**: For version control [Download Git](https://git-scm.com/downloads)

## Quick Start Guide

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd My_SaaS_Frontend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
# Copy the example environment file
cp .env.example .env
```

Edit the `.env` file with your configuration:
```env
# Backend API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# Development Settings
VITE_ENV=development
```

### 4. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5173` (or the next available port).

### 5. Backend Setup
**Important**: This frontend requires the backend to be running. Please follow the setup instructions in the `My_SaaS_Backend` repository to start the backend service.

## Complete Development Setup

### Option 1: Local Development (Recommended for Frontend Development)

1. **Setup Backend First**:
   - Navigate to your backend directory: `cd ../My_SaaS_Backend`
   - Follow the backend README setup instructions
   - Ensure backend is running on `http://localhost:8000`

2. **Setup Frontend**:
   ```bash
   # Install dependencies
   npm install

   # Copy and configure environment
   cp .env.example .env

   # Start development server
   npm run dev
   ```

### Option 2: Docker Development (Frontend Only)

If you prefer to run the frontend in Docker:

1. **Create a Dockerfile** in the frontend root:
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   EXPOSE 3000
   CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"]
   ```

2. **Build and run the Docker container**:
   ```bash
   # Build the image
   docker build -t my-saas-frontend .

   # Run the container
   docker run -p 3000:3000 --env-file .env my-saas-frontend
   ```

3. **Alternative: Using docker-compose**:
   ```yaml
   # docker-compose.yml
   version: '3.8'
   services:
     frontend:
       build: .
       ports:
         - "3000:3000"
       env_file:
         - .env
       volumes:
         - .:/app
         - /app/node_modules
   ```

   ```bash
   docker-compose up --build
   ```

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint

# Install new dependencies
npm install <package-name>

# Update dependencies
npm update
```

## Project Structure

```
My_SaaS_Frontend/
├── public/                 # Static assets
│   └── vite.svg           # Vite logo
├── src/                   # Source code
│   ├── components/        # Reusable UI components
│   ├── hooks/            # Custom React hooks
│   │   └── useApi.js     # API integration hooks
│   ├── services/         # API service layer
│   │   └── api.js        # Axios configuration and API functions
│   ├── assets/           # Images, fonts, etc.
│   ├── App.jsx           # Main application component
│   ├── App.css           # Application styles
│   ├── index.css         # Global styles
│   └── main.jsx          # Application entry point
├── .env.example          # Environment variables template
├── .env                  # Environment variables (create from .env.example)
├── package.json          # Dependencies and scripts
├── vite.config.js        # Vite configuration
├── eslint.config.js      # ESLint configuration
└── README.md             # This file
```

## API Integration

The frontend expects the following API endpoints from your backend:

### Chat Management
- `GET /chats` - Get user's chat history
- `POST /chats` - Create new chat
- `GET /chats/{id}` - Get specific chat with messages
- `PUT /chats/{id}` - Update chat title
- `DELETE /chats/{id}` - Delete chat
- `POST /chats/{id}/messages` - Send message and get AI response

### Database Integration
- `GET /database/tables` - Get available database tables
- `GET /database/tables/{name}/schema` - Get table schema
- `POST /database/query` - Execute natural language query

### Authentication (Optional)
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /auth/me` - Get current user info

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Backend API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# Development Settings
VITE_ENV=development
```

### Environment Variables Description

- `VITE_API_BASE_URL` - Backend API base URL (default: http://localhost:8000)
- `VITE_API_TIMEOUT` - API request timeout in milliseconds (default: 10000)
- `VITE_ENV` - Environment (development/production)

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   # Kill process using port 5173
   lsof -ti:5173 | xargs kill -9
   ```

2. **Dependencies issues**:
   ```bash
   # Clear npm cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Backend connection issues**:
   - Ensure backend is running on the correct port
   - Check CORS settings in backend
   - Verify API base URL in `.env` file

4. **Build issues**:
   ```bash
   # Clear Vite cache
   rm -rf dist .vite
   npm run build
   ```

## Production Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Static Hosting (Netlify, Vercel, etc.)
1. Build the project: `npm run build`
2. Upload the `dist` folder to your hosting service
3. Configure environment variables on your hosting platform

### Deploy with Docker
```bash
# Build production image
docker build -t my-saas-frontend .

# Run container
docker run -p 3000:3000 my-saas-frontend
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Run tests: `npm run lint`
5. Commit your changes: `git commit -m 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Ensure your backend API is properly configured and running
3. Check the browser console for error messages
4. Verify your environment variables are correctly set
5. Test API connectivity using the browser's network tab

## Backend Requirements

This frontend requires a compatible backend API that provides:

### Required Endpoints
- `GET /chats` - Get user's chat history
- `POST /chats` - Create new chat
- `GET /chats/{id}` - Get specific chat with messages
- `PUT /chats/{id}` - Update chat title
- `DELETE /chats/{id}` - Delete chat
- `POST /chats/{id}/messages` - Send message and get AI response
- `GET /database/tables` - Get available database tables
- `GET /database/tables/{name}/schema` - Get table schema
- `POST /database/query` - Execute natural language query

### CORS Configuration
Make sure your backend allows requests from your frontend domain:
```python
# Example FastAPI CORS configuration
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```
