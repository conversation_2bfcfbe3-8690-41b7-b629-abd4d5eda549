# My SaaS Frontend

A React-based chat interface for AI-powered database query generation using natural language. This frontend connects to a FastAPI + <PERSON><PERSON><PERSON>n backend that converts natural language queries into SQL.

## Features

- **Chat Interface**: Create and manage multiple chat sessions
- **Database Integration**: Browse database tables and schemas
- **Natural Language Queries**: Send natural language queries to generate SQL
- **Real-time Responses**: Get AI-generated responses and query results
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: React 19 + Vite
- **HTTP Client**: Axios for API communication
- **Styling**: Custom CSS with responsive design
- **State Management**: React hooks with custom API hooks

## Backend Integration

This frontend is designed to work with the `My_SaaS_Backend` FastAPI application that provides:
- Chat management endpoints
- Database schema introspection
- Natural language to SQL conversion using LangChain
- Query execution and result formatting

## Setup Instructions

1. **Clone and install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment variables**:
   ```bash
   cp .env.example .env
   ```
   Update the `.env` file with your backend URL:
   ```
   VITE_API_BASE_URL=http://localhost:8000
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Ensure your backend is running**:
   Make sure your FastAPI backend is running on the configured URL (default: http://localhost:8000)

## API Integration

The frontend expects the following API endpoints from your backend:

### Chat Management
- `GET /chats` - Get user's chat history
- `POST /chats` - Create new chat
- `GET /chats/{id}` - Get specific chat with messages
- `PUT /chats/{id}` - Update chat title
- `DELETE /chats/{id}` - Delete chat
- `POST /chats/{id}/messages` - Send message and get AI response

### Database Integration
- `GET /database/tables` - Get available database tables
- `GET /database/tables/{name}/schema` - Get table schema
- `POST /database/query` - Execute natural language query

### Authentication (Optional)
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /auth/me` - Get current user info

## Project Structure

```
src/
├── components/          # Reusable UI components
├── hooks/              # Custom React hooks
│   └── useApi.js       # API integration hooks
├── services/           # API service layer
│   └── api.js          # Axios configuration and API functions
├── App.jsx             # Main application component
├── App.css             # Application styles
└── main.jsx            # Application entry point
```

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Environment Variables

- `VITE_API_BASE_URL` - Backend API base URL (default: http://localhost:8000)
- `VITE_API_TIMEOUT` - API request timeout in milliseconds (default: 10000)
- `VITE_ENV` - Environment (development/production)
