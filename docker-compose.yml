version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://backend:8000
    depends_on:
      - backend
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - app-network

  backend:
    build: ../My_SaaS_Backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DB_URI=mysql+pymysql://user:password@db:3306/dev_db
    depends_on:
      - db
    volumes:
      - ../My_SaaS_Backend:/app
    networks:
      - app-network

  db:
    image: mysql/mysql-server:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: dev_db
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data:

networks:
  app-network:
    driver: bridge
